server:
  port: 8080

JWT_SECRET: ""
jwt:
  secret:

jwtExpiryDays: 7


JWT_ALGORITHM: "RS256"

maximumIdleTime: "30s"
maximumLifeTime: "5m"
acquiringTimeOut: "10s"
evictInBackground: "30s"

grpctimeout: "5s"

maximumConnections: 500

keepAliveTime: 1000
keepAliveTimeOut: 10000
connectTimeOut: 5000
readHandlerTimeout: 10000
writeHandlerTimeout: 10000

CA_CERT: "_DO_NOT_COMMIT"
MTLS_KEY: "_DO_NOT_COMMIT"
MTLS_CERT: "_DO_NOT_COMMIT"
cache:
  maximum-size: 1000
  expire-after-write: 10
  expire-service-account-permissions: 5

demo:
  tenant:
    id: "39e868b6-fdfc-4118-b664-a7d4b04728e8"

resource-insights:
  auth-key:
    enabled: false
    value: "_DO_NOT_COMMIT"
  tenant-mapping:
    sourceTenantId: "_DO_NOT_COMMIT"
    targetTenantId: "_DO_NOT_COMMIT"

grpc:
  authservice:
    host: localhost
    port: 9090
    enable-mtls: false

# MTLS certificates (not needed when enable-mtls is false, but required by @Value)
MTLS_CERT: ""
CA_CERT: ""
MTLS_KEY: ""

spring:
  application:
    name: gateway-connector

  cloud:
    gateway:
      routes:
        - id: nimbus-route
          uri: https://dev-nimbus.cloud.ilabs.io
          predicates:
            - Header=X-api-key, .+
            - Header=X-api-secret, .+
          filters:
            - name: PermissionFilter
            - name: NimbusFilter
              args:
                message: "This is a service account request for nimbus"

        - id: asgard-service
          uri: http://localhost:8080
          predicates:
            - Path=/api/asgard/**
          filters:
            - name: AsgardFilter
              args:
                message: "Inside trpc route"
            - StripPrefix=2

        - id: Integrations
          uri: http://localhost:8080
          predicates:
            - Path=/api/integrations/**
          filters:
            - name: IntegrationsFilter
              args:
                message: "PlaceHolder for Integrations"
            - StripPrefix=2

        - id: MachineLearning
          uri: http://localhost:8080
          predicates:
            - Path=/api/ML/**
          filters:
            - name: MachineLearningFilter
              args:
                message: "PlaceHolder for ML"
            - StripPrefix=2

        - id: CloudSecure
          uri: http://localhost:8080
          predicates:
            - Path=/cloudsecure/**
          filters:
            - name: PermissionFilter
            - name: CloudSecureFilter
              args:
                message: "Cloudsecure permissions"





        - id: insights-search-service
          uri: https://dev-gw.console.ilabs.io
          predicates:
            - Path=/api/v1/search/tenant/**
          filters:
            - name: PermissionFilter
            - name: DemoModeFilter
              args:
                message: "Demo mode filter for insights search"
            - name: InsightsSearchFilter
              args:
                message: "Filter for Insights search Service"
            - name: BackendErrorHandlingFilter
              args:
                serviceName: "insights-search-service"
                includeServiceInfo: true
                includeErrorDetails: false
#                healthCheckUrl: "https://insights.sunnyvale.ilabs.io/health"
            - RewritePath=/api/v1/search/tenant/(?<remaining>.*), /api/v1/search/tenant/${remaining}

        - id: resource-insights-service
          uri: http://localhost:8080
          predicates:
            - Path=/api/v1/resource-insights**
          filters:
            - name: ResourceInsightsFilter
              args:
                message: "Filter for Resource Insights Service"

#         Adding Core filter
        - id: core-service
          uri: http://localhost:8080  # This gets overridden
          predicates:
            - Path=/core/**
          filters:
            - name: PermissionFilter
            - name: CoreFilter
              args:
                message: "Routing core"

        - id: magic-link-auth-service
          uri: http://localhost:8080
          predicates:
            - Path=/api/magiclink/**
            - Header=user_uuid, .+
            - Header=uc_session, .+
            - Header=ucid, .+
            - Header=X-csi, .+

          filters:
            - name: MagicLinkAuthFilter
              args:
                message: "Placeholder for magic link auth"

      forwarded:
        enabled: true

grpc:
  authservice:
    host: localhost
    port: 8081

management:
  endpoints:
    web:
      exposure:
        include: health, info
  endpoint:
    health:
      show-details: always
      probes:
        enabled: true

logging:
  level:
    root: INFO
    org.springframework.web.reactive.function.client.ExchangeFunctions: DEBUG
    org.springframework.cloud.gateway.filter: DEBUG
    org.springframework.cloud.gateway: TRACE
    reactor.netty.http.server: DEBUG
    reactor.netty: DEBUG