#!/bin/bash

# Kafka Simulation Script for ip-classification-mmdb service
# This script helps set up and run Kafka simulations for testing consumer timeout,
# rebalancing, and partition assignment scenarios.

set -e

# Configuration
KAFKA_BOOTSTRAP_SERVERS="localhost:9092"
INPUT_TOPIC="decorated-flow-v1"
OUTPUT_TOPIC="ip-classified-flow-v1"
PARTITIONS=3
REPLICATION_FACTOR=1

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${BLUE}================================================${NC}"
    echo -e "${BLUE}  Kafka Simulation for ip-classification-mmdb${NC}"
    echo -e "${BLUE}================================================${NC}"
    echo ""
}

print_section() {
    echo -e "${YELLOW}--- $1 ---${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

check_kafka() {
    print_section "Checking Kafka Connection"
    
    if kafka-topics.sh --bootstrap-server $KAFKA_BOOTSTRAP_SERVERS --list > /dev/null 2>&1; then
        print_success "Kafka is running on $KAFKA_BOOTSTRAP_SERVERS"
        return 0
    else
        print_error "Cannot connect to Kafka on $KAFKA_BOOTSTRAP_SERVERS"
        print_info "Please ensure Kafka is running. You can start it with:"
        print_info "  docker-compose up kafka  # if using Docker"
        print_info "  or start Kafka manually"
        return 1
    fi
}

create_topics() {
    print_section "Creating Kafka Topics"
    
    # Create input topic
    if kafka-topics.sh --bootstrap-server $KAFKA_BOOTSTRAP_SERVERS --describe --topic $INPUT_TOPIC > /dev/null 2>&1; then
        print_info "Topic $INPUT_TOPIC already exists"
    else
        kafka-topics.sh --bootstrap-server $KAFKA_BOOTSTRAP_SERVERS \
            --create --topic $INPUT_TOPIC \
            --partitions $PARTITIONS \
            --replication-factor $REPLICATION_FACTOR
        print_success "Created topic: $INPUT_TOPIC"
    fi
    
    # Create output topic
    if kafka-topics.sh --bootstrap-server $KAFKA_BOOTSTRAP_SERVERS --describe --topic $OUTPUT_TOPIC > /dev/null 2>&1; then
        print_info "Topic $OUTPUT_TOPIC already exists"
    else
        kafka-topics.sh --bootstrap-server $KAFKA_BOOTSTRAP_SERVERS \
            --create --topic $OUTPUT_TOPIC \
            --partitions $PARTITIONS \
            --replication-factor $REPLICATION_FACTOR
        print_success "Created topic: $OUTPUT_TOPIC"
    fi
    
    # Show topic details
    echo ""
    print_info "Topic details:"
    kafka-topics.sh --bootstrap-server $KAFKA_BOOTSTRAP_SERVERS --describe --topic $INPUT_TOPIC
    kafka-topics.sh --bootstrap-server $KAFKA_BOOTSTRAP_SERVERS --describe --topic $OUTPUT_TOPIC
}

start_test_producer() {
    print_section "Starting Test Message Producer"
    
    print_info "Starting background producer to send test messages..."
    print_info "This will send sample flow messages to $INPUT_TOPIC every 2 seconds"
    
    # Start producer in background
    (
        counter=1
        while true; do
            message="{\"flow_id\":\"flow-$counter\",\"src_ip\":\"192.168.1.$((counter % 254 + 1))\",\"dst_ip\":\"10.0.0.$((counter % 254 + 1))\",\"timestamp\":\"$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)\",\"bytes\":$((RANDOM % 10000 + 1000))}"
            echo "$message" | kafka-console-producer.sh --bootstrap-server $KAFKA_BOOTSTRAP_SERVERS --topic $INPUT_TOPIC > /dev/null 2>&1
            echo "Sent message $counter: flow-$counter"
            counter=$((counter + 1))
            sleep 2
        done
    ) &
    
    PRODUCER_PID=$!
    echo $PRODUCER_PID > /tmp/kafka_producer.pid
    print_success "Test producer started (PID: $PRODUCER_PID)"
    print_info "Producer will send messages until stopped with: kill $PRODUCER_PID"
}

stop_test_producer() {
    if [ -f /tmp/kafka_producer.pid ]; then
        PRODUCER_PID=$(cat /tmp/kafka_producer.pid)
        if kill -0 $PRODUCER_PID 2>/dev/null; then
            kill $PRODUCER_PID
            rm -f /tmp/kafka_producer.pid
            print_success "Test producer stopped"
        else
            print_info "Test producer was not running"
            rm -f /tmp/kafka_producer.pid
        fi
    else
        print_info "No producer PID file found"
    fi
}

run_simulation_test() {
    local test_name=$1
    print_section "Running Simulation: $test_name"
    
    cd "$(dirname "$0")"
    
    case $test_name in
        "timeout")
            print_info "Running consumer timeout simulation..."
            ./gradlew test --tests IpClassificationKafkaSimulatorTest.testConsumerTimeout -Dkafka.simulation.enabled=true
            ;;
        "rebalance")
            print_info "Running consumer group rebalance simulation..."
            ./gradlew test --tests IpClassificationKafkaSimulatorTest.testConsumerGroupRebalance -Dkafka.simulation.enabled=true
            ;;
        "partitions")
            print_info "Running partition assignment logging test..."
            ./gradlew test --tests IpClassificationKafkaSimulatorTest.testPartitionAssignmentLogging -Dkafka.simulation.enabled=true
            ;;
        "all")
            print_info "Running all simulations..."
            ./gradlew test --tests IpClassificationKafkaSimulatorTest.testAllSimulations -Dkafka.simulation.enabled=true
            ;;
        "custom")
            print_info "Running custom configuration test..."
            ./gradlew test --tests IpClassificationKafkaSimulatorTest.testWithCustomConfiguration -Dkafka.simulation.enabled=true
            ;;
        *)
            print_error "Unknown test: $test_name"
            print_info "Available tests: timeout, rebalance, partitions, all, custom"
            return 1
            ;;
    esac
}

show_consumer_groups() {
    print_section "Consumer Group Information"
    
    print_info "Listing all consumer groups:"
    kafka-consumer-groups.sh --bootstrap-server $KAFKA_BOOTSTRAP_SERVERS --list
    
    echo ""
    print_info "Detailed information for simulation consumer groups:"
    
    for group in "ip-classification-mmdb-simulator" "ip-classification-mmdb-simulator-timeout" "partition-assignment-test-group"; do
        echo ""
        print_info "Consumer group: $group"
        if kafka-consumer-groups.sh --bootstrap-server $KAFKA_BOOTSTRAP_SERVERS --group $group --describe 2>/dev/null; then
            print_success "Group $group found"
        else
            print_info "Group $group not found (may not have been created yet)"
        fi
    done
}

cleanup() {
    print_section "Cleanup"
    
    stop_test_producer
    
    print_info "Consumer groups will remain for inspection. To delete them:"
    print_info "  kafka-consumer-groups.sh --bootstrap-server $KAFKA_BOOTSTRAP_SERVERS --group <group-name> --delete"
    
    print_info "Topics will remain for reuse. To delete them:"
    print_info "  kafka-topics.sh --bootstrap-server $KAFKA_BOOTSTRAP_SERVERS --delete --topic $INPUT_TOPIC"
    print_info "  kafka-topics.sh --bootstrap-server $KAFKA_BOOTSTRAP_SERVERS --delete --topic $OUTPUT_TOPIC"
}

show_help() {
    print_header
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  setup           - Check Kafka and create topics"
    echo "  producer        - Start test message producer"
    echo "  stop-producer   - Stop test message producer"
    echo "  test <name>     - Run specific simulation test"
    echo "  groups          - Show consumer group information"
    echo "  cleanup         - Stop producer and show cleanup instructions"
    echo "  help            - Show this help message"
    echo ""
    echo "Test names:"
    echo "  timeout         - Consumer timeout simulation"
    echo "  rebalance       - Consumer group rebalance simulation"
    echo "  partitions      - Partition assignment logging"
    echo "  all             - Run all simulations"
    echo "  custom          - Custom configuration test"
    echo ""
    echo "Examples:"
    echo "  $0 setup                    # Set up Kafka topics"
    echo "  $0 producer                 # Start sending test messages"
    echo "  $0 test timeout             # Run timeout simulation"
    echo "  $0 test all                 # Run all simulations"
    echo "  $0 groups                   # Check consumer groups"
    echo "  $0 cleanup                  # Clean up"
}

# Main script logic
case "${1:-help}" in
    "setup")
        print_header
        check_kafka && create_topics
        ;;
    "producer")
        print_header
        check_kafka && start_test_producer
        ;;
    "stop-producer")
        print_header
        stop_test_producer
        ;;
    "test")
        if [ -z "$2" ]; then
            print_error "Please specify a test name"
            show_help
            exit 1
        fi
        print_header
        check_kafka && run_simulation_test "$2"
        ;;
    "groups")
        print_header
        check_kafka && show_consumer_groups
        ;;
    "cleanup")
        print_header
        cleanup
        ;;
    "help"|*)
        show_help
        ;;
esac
