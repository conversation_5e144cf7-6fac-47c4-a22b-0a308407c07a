package com.illumio.data;

import com.illumio.data.components.IpClassificationFlowWriter;
import com.illumio.data.components.IpClassificationLookup;
import com.illumio.data.components.ResourceIdFlowReader;
import com.illumio.data.configuration.IpClassificationMmdbConfig;
import com.illumio.data.util.FlowDataValidator;
import com.illumio.data.util.MetricsUtil;
import com.fasterxml.jackson.databind.JsonNode;
import io.opentelemetry.api.common.Attributes;

import lombok.extern.slf4j.Slf4j;

import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.RetriableCommitFailedException;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.errors.TimeoutException;
import org.apache.kafka.common.errors.DisconnectException;
import org.apache.kafka.common.errors.NetworkException;
import org.apache.kafka.common.errors.RetriableException;
import org.springframework.stereotype.Component;

import reactor.core.Disposable;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Scheduler;
import reactor.kafka.receiver.KafkaReceiver;
import reactor.kafka.receiver.ReceiverOptions;
import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderRecord;
import reactor.kafka.sender.SenderResult;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Supplier;

@Slf4j
@Component
public class IpClassificationMmdbPipeline {
    private final Supplier<KafkaReceiver<String, String>> kafkaReceiverFactory;
    private final ResourceIdFlowReader resourceIdFlowReader;
    private final IpClassificationLookup ipClassificationLookup;
    private final IpClassificationFlowWriter ipClassificationFlowWriter;
    private final String sinkTopic;
    private final IpClassificationMmdbConfig ipClassificationMmdbConfig;
    private final Scheduler ipClassificationScheduler;
    private final List<KafkaSender<String, String>> kafkaSenders;
    private final MetricsUtil metricsUtil;
    private final AtomicInteger currentSenderIndex = new AtomicInteger(0);

    private final AtomicReference<KafkaReceiver<String, String>> currentKafkaReceiver = new AtomicReference<>();
    private volatile Disposable disposable;

    public IpClassificationMmdbPipeline(
            KafkaReceiver<String, String> kafkaReceiver,
            List<KafkaSender<String, String>> kafkaSenders,
            ResourceIdFlowReader resourceIdFlowReader,
            IpClassificationLookup ipClassificationLookup,
            IpClassificationFlowWriter ipClassificationFlowWriter,
            IpClassificationMmdbConfig ipClassificationMmdbConfig,
            Scheduler ipClassificationScheduler,
            MetricsUtil metricsUtil) {
        // Create a factory to recreate receivers when needed
        this.kafkaReceiverFactory = () -> createKafkaReceiver(ipClassificationMmdbConfig);
        this.currentKafkaReceiver.set(kafkaReceiver);
        this.resourceIdFlowReader = resourceIdFlowReader;
        this.ipClassificationLookup = ipClassificationLookup;
        this.ipClassificationFlowWriter = ipClassificationFlowWriter;
        this.sinkTopic = getSinkTopic(ipClassificationMmdbConfig);
        this.ipClassificationMmdbConfig = ipClassificationMmdbConfig;
        this.ipClassificationScheduler = ipClassificationScheduler;
        this.kafkaSenders = kafkaSenders;
        this.metricsUtil = metricsUtil;
    }

    /**
     * Fail early if sink topic is not defined.
     *
     * @param ipClassificationMmdbConfig
     * @return
     */
    private String getSinkTopic(IpClassificationMmdbConfig ipClassificationMmdbConfig) {
        return Optional.of(ipClassificationMmdbConfig)
                .map(IpClassificationMmdbConfig::getKafkaSenderConfig)
                .map(IpClassificationMmdbConfig.KafkaSenderConfig::getSinkTopic)
                .orElseThrow(
                        () ->
                                new NoSuchElementException(
                                        "No flows topic configured. Please make sure "
                                                + "ipClassificationMmdb.kafkaSenderConfig.sinkTopic is set."));
    }

    private KafkaSender<String, String> getNextSender() {
        return kafkaSenders.get(
                currentSenderIndex.getAndUpdate(current ->
                        (current + 1) % kafkaSenders.size())
        );
    }

    /**
     * Creates a new Kafka receiver with the same configuration
     */
    private KafkaReceiver<String, String> createKafkaReceiver(IpClassificationMmdbConfig config) {
        // Recreate receiver options using the same configuration
        ReceiverOptions<String, String> receiverOptions = ReceiverOptions
                .<String, String>create(createConsumerProps(config))
                .subscription(java.util.Collections.singleton(config.getKafkaReceiverConfig().getTopic()));

        return KafkaReceiver.create(receiverOptions);
    }

    /**
     * Creates consumer properties for Kafka receiver
     */
    private java.util.Map<String, Object> createConsumerProps(IpClassificationMmdbConfig config) {
        java.util.Map<String, Object> consumerProps = new java.util.HashMap<>();
        consumerProps.put(org.apache.kafka.clients.consumer.ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG,
                config.getKafkaCommonConfig().getBootstrapServers());
        consumerProps.put(org.apache.kafka.clients.consumer.ConsumerConfig.GROUP_ID_CONFIG,
                config.getKafkaReceiverConfig().getGroupId());
        if (config.getKafkaReceiverConfig().getGroupInstanceId() != null) {
            consumerProps.put(org.apache.kafka.clients.consumer.ConsumerConfig.GROUP_INSTANCE_ID_CONFIG,
                    config.getKafkaReceiverConfig().getGroupInstanceId());
        }
        consumerProps.put(org.apache.kafka.clients.consumer.ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG,
                org.apache.kafka.common.serialization.StringDeserializer.class);
        consumerProps.put(org.apache.kafka.clients.consumer.ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG,
                org.apache.kafka.common.serialization.StringDeserializer.class);
        consumerProps.put(org.apache.kafka.clients.consumer.ConsumerConfig.REQUEST_TIMEOUT_MS_CONFIG,
                config.getKafkaReceiverConfig().getRequestTimeoutMs());
        consumerProps.put(org.apache.kafka.clients.consumer.ConsumerConfig.AUTO_OFFSET_RESET_CONFIG,
                config.getKafkaReceiverConfig().getAutoOffsetReset());
        consumerProps.put(org.apache.kafka.clients.consumer.ConsumerConfig.MAX_POLL_RECORDS_CONFIG,
                config.getKafkaReceiverConfig().getMaxPollRecords());
        consumerProps.put(org.apache.kafka.clients.consumer.ConsumerConfig.MAX_PARTITION_FETCH_BYTES_CONFIG,
                config.getKafkaReceiverConfig().getMaxPartitionFetchBytes());
        consumerProps.put(org.apache.kafka.clients.consumer.ConsumerConfig.PARTITION_ASSIGNMENT_STRATEGY_CONFIG,
                config.getKafkaReceiverConfig().getAssignmentStrategy());
        consumerProps.put(org.apache.kafka.clients.consumer.ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG,
                config.getKafkaReceiverConfig().getMaxPollIntervalMs());
        consumerProps.put(org.apache.kafka.clients.consumer.ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG,
                config.getKafkaReceiverConfig().getSessionTimeoutMs());
        consumerProps.put(org.apache.kafka.clients.consumer.ConsumerConfig.HEARTBEAT_INTERVAL_MS_CONFIG,
                config.getKafkaReceiverConfig().getHeartbeatIntervalMs());
        consumerProps.put(org.apache.kafka.clients.CommonClientConfigs.METADATA_MAX_AGE_CONFIG,
                config.getKafkaCommonConfig().getMetadataMaxAgeMs());
        consumerProps.put(org.apache.kafka.clients.CommonClientConfigs.CONNECTIONS_MAX_IDLE_MS_CONFIG,
                config.getKafkaCommonConfig().getConnectionMaxIdleMs());

        consumerProps.put(ConsumerConfig.DEFAULT_API_TIMEOUT_MS_CONFIG, 1000);

        // Add SASL configuration if needed
        if (config.getKafkaCommonConfig().getIsSasl() != null && config.getKafkaCommonConfig().getIsSasl()) {
            consumerProps.put(org.apache.kafka.clients.CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_SSL");
            consumerProps.put(org.apache.kafka.common.config.SaslConfigs.SASL_MECHANISM, "PLAIN");
            consumerProps.put(org.apache.kafka.common.config.SaslConfigs.SASL_JAAS_CONFIG,
                    config.getKafkaCommonConfig().getSaslJaasConfig());
        }

        return consumerProps;
    }

    /**
     * Recreates the Kafka receiver and restarts the pipeline
     */
    private void recreateConsumerAndRestart() {
        log.warn("🔄 Recreating Kafka consumer due to timeout/network issues...");

        // Dispose current subscription if exists
        if (disposable != null && !disposable.isDisposed()) {
            disposable.dispose();
            log.info("🛑 Disposed current consumer subscription");
        }

        try {
            // Create new receiver
            KafkaReceiver<String, String> newReceiver = kafkaReceiverFactory.get();
            KafkaReceiver<String, String> oldReceiver = currentKafkaReceiver.getAndSet(newReceiver);

            log.info("✅ Created new Kafka receiver, restarting pipeline...");

            // Restart with new receiver - add error handling for the restart
            this.disposable = startInternal()
                    .doOnError(error -> {
                        log.error("💥 Error during pipeline restart: {}", error.getMessage(), error);
                        if (isTimeoutOrNetworkError(error)) {
                            log.warn("🔄 Restart failed due to timeout/network error, will retry recreation in 5 seconds");
                            Mono.delay(Duration.ofSeconds(5))
                                    .doOnNext(tick -> recreateConsumerAndRestart())
                                    .subscribe();
                        }
                    })
                    .subscribe(
                            result -> log.debug("Pipeline restart successful: {}", result),
                            error -> log.error("💥 Pipeline restart failed: {}", error.getMessage(), error),
                            () -> log.info("Pipeline restart completed")
                    );

            log.info("🚀 Pipeline restarted with new consumer");

        } catch (Exception e) {
            log.error("💥 Failed to recreate consumer: {}", e.getMessage(), e);
            // Retry recreation after delay
            Mono.delay(Duration.ofSeconds(5))
                    .doOnNext(tick -> {
                        log.info("🔄 Retrying consumer recreation after failure...");
                        recreateConsumerAndRestart();
                    })
                    .subscribe();
        }
    }

    /**
     * Checks if the error is a timeout or network-related issue that should trigger consumer recreation
     */
    private boolean isTimeoutOrNetworkError(Throwable error) {
        return error instanceof TimeoutException ||
               error instanceof DisconnectException ||
               error instanceof NetworkException ||
               error instanceof RetriableCommitFailedException ||
               error instanceof RetriableException ||
               error instanceof java.util.concurrent.TimeoutException ||
               error instanceof java.net.SocketTimeoutException ||
               error instanceof java.io.IOException ||
               (error.getCause() != null && isTimeoutOrNetworkError(error.getCause())) ||
               error.getMessage() != null && (
                   error.getMessage().toLowerCase().contains("timeout") ||
                   error.getMessage().toLowerCase().contains("network") ||
                   error.getMessage().toLowerCase().contains("connection") ||
                   error.getMessage().toLowerCase().contains("disconnect") ||
                   error.getMessage().toLowerCase().contains("commit failed") ||
                   error.getMessage().toLowerCase().contains("retriable")
               );
    }

    public void start() {
        this.disposable = startInternal().subscribe();
    }

    public Flux<SenderResult<String>> startInternal() {
        KafkaReceiver<String, String> receiver = currentKafkaReceiver.get();

        Flux<ConsumerRecord<String, String>> receiverFlux =
                receiver
                        .receiveAutoAck()
                        .publishOn(ipClassificationScheduler)
                        .doOnError(error -> {
                            log.error("🚨 Error in Kafka receiver (receiveAutoAck): {}", error.getMessage(), error);
                            if (isTimeoutOrNetworkError(error)) {
                                log.warn("🔄 Receiver-level timeout/network error detected, will recreate consumer: {}",
                                        error.getMessage());
                            }
                        })
                        .onErrorResume(error -> {
                            if (isTimeoutOrNetworkError(error)) {
                                log.warn("🔄 Handling receiver-level error by recreating consumer: {}", error.getMessage());
                                // Schedule consumer recreation asynchronously
                                Mono.delay(Duration.ofSeconds(1))
                                        .doOnNext(tick -> recreateConsumerAndRestart())
                                        .subscribe();
                                // Return empty flux to terminate current stream gracefully
                                return Flux.empty();
                            }
                            // For non-timeout errors, propagate the error
                            return Flux.error(error);
                        })
                        .flatMap(records -> records);

        // Apply backpressure configuration if enabled
        if (ipClassificationMmdbConfig.getBackpressureConfig().isEnabled()) {
            receiverFlux =
                    receiverFlux
                            .limitRate(
                                    ipClassificationMmdbConfig
                                            .getBackpressureConfig()
                                            .getHighTide(),
                                    ipClassificationMmdbConfig.getBackpressureConfig().getLowTide())
                            .bufferTimeout(
                                    ipClassificationMmdbConfig.getBackpressureConfig().getMaxSize(),
                                    ipClassificationMmdbConfig
                                            .getBackpressureConfig()
                                            .getMaxTime())
                            .flatMap(Flux::fromIterable);
        }

        return getNextSender()
                .send(
                        receiverFlux
                                .doOnSubscribe(__ -> log.info("IpClassificationMmdbPipeline started for receiver."))
                                .doOnComplete(() -> log.info("IpClassificationMmdbPipeline completed normally."))
                                .doOnCancel(() -> log.warn("IpClassificationMmdbPipeline was cancelled unexpectedly."))
                                .doOnError(e -> {
                                    log.error("IpClassificationMmdbPipeline terminated due to error: {}", e.getMessage(), e);
                                    // Check if this is a timeout/network error that should trigger consumer recreation
                                    if (isTimeoutOrNetworkError(e)) {
                                        log.warn("🚨 Detected timeout/network error, will recreate consumer: {}", e.getMessage());
                                    }
                                })
                                .retryWhen(
                                        Retry.backoff(
                                                        ipClassificationMmdbConfig
                                                                .getKafkaReceiverConfig()
                                                                .getMaxRetries(),
                                                        ipClassificationMmdbConfig
                                                                .getKafkaReceiverConfig()
                                                                .getMaxBackoff())
                                                .filter(throwable -> {
                                                    // For timeout/network errors, recreate consumer instead of retrying
                                                    if (isTimeoutOrNetworkError(throwable)) {
                                                        log.warn("🔄 Timeout/network error detected, recreating consumer instead of retrying: {}",
                                                                throwable.getMessage());
                                                        // Schedule consumer recreation asynchronously
                                                        Mono.delay(Duration.ofSeconds(2))
                                                                .doOnNext(tick -> recreateConsumerAndRestart())
                                                                .subscribe();
                                                        return false; // Don't retry, we're recreating
                                                    }
                                                    return true; // Allow retry for other errors
                                                })
                                                .doBeforeRetry(
                                                        retrySignal ->
                                                                log.warn(
                                                                        "Error receiving from Kafka, retrying... {} (attempt {})",
                                                                        retrySignal.failure().getMessage(),
                                                                        retrySignal.totalRetries() + 1)))
                                .flatMap(this::processConsumerRecord))
                        .doOnError(error -> {
                            log.error("🚨 Error in sender pipeline: {}", error.getMessage(), error);
                            if (isTimeoutOrNetworkError(error)) {
                                log.warn("🔄 Sender-level timeout/network error, recreating consumer: {}", error.getMessage());
                                Mono.delay(Duration.ofSeconds(1))
                                        .doOnNext(tick -> recreateConsumerAndRestart())
                                        .subscribe();
                            }
                        })
                        .onErrorResume(error -> {
                            if (isTimeoutOrNetworkError(error)) {
                                log.warn("🔄 Handling sender-level error by recreating consumer: {}", error.getMessage());
                                Mono.delay(Duration.ofSeconds(1))
                                        .doOnNext(tick -> recreateConsumerAndRestart())
                                        .subscribe();
                                return Flux.empty(); // Terminate current stream gracefully
                            }
                            return Flux.error(error); // Propagate non-timeout errors
                        })
                        .retryWhen(
                                Retry.backoff(
                                        ipClassificationMmdbConfig
                                                .getKafkaSenderConfig()
                                                .getMaxRetries(),
                                        ipClassificationMmdbConfig
                                                .getKafkaSenderConfig()
                                                .getMaxBackoff())
                                        .filter(throwable -> !isTimeoutOrNetworkError(throwable)) // Don't retry timeout errors
                                        .doBeforeRetry(
                                                retrySignal ->
                                                        log.warn(
                                                                "Error sending to Kafka, retrying... {}",
                                                                retrySignal)));
    }

    public Mono<SenderRecord<String, String, String>> processConsumerRecord(
            ConsumerRecord<String, String> consumerRecord) {
        return Mono.just(consumerRecord)
                .doOnNext(__ -> log.debug("Processing record {}", consumerRecord))
                .flatMap(
                        __ ->
                                resourceIdFlowReader
                                        .readTree(consumerRecord.value())
                                        .doOnError(
                                                throwable ->
                                                        log.debug(
                                                                "Error parsing record {}",
                                                                consumerRecordString(
                                                                        consumerRecord),
                                                                throwable))
                                        .flatMap(
                                                node ->
                                                        ipClassificationLookup
                                                                .maybeAddIpClassification(node)
                                                                .doOnError(
                                                                        throwable ->
                                                                                log.debug(
                                                                                        "IP Classification Lookup failed: [{}] for record key [{}]",
                                                                                        throwable,
                                                                                        consumerRecord
                                                                                                .key()))
                                                                .doOnNext(enrichedRecord -> {
                                                                    // Extract tenant ID for outgoing metrics
                                                                    String tenantId = FlowDataValidator.extractTenantId(enrichedRecord);
                                                                    Attributes tenantAttributes = Attributes.builder()
                                                                            .put(FlowDataValidator.INSIGHTS_TENANT_ID, tenantId)
                                                                            .build();
                                                                    metricsUtil.incrementIPClassificationOutgoingEvent(tenantAttributes);
                                                                }))
                                        .flatMap(ipClassificationFlowWriter::writeTreeAsString))
                .doOnError(
                        throwable -> {
                                log.warn(
                                        "Error processing record: {}",
                                        throwable
                                                .getClass()
                                                .getSimpleName()); // if it reaches here, it seems
                        })
                .onErrorReturn(consumerRecord.value())
                .flatMap(
                        newValue ->
                        {
                                return Mono.just(
                                        SenderRecord.create(
                                                new ProducerRecord<>(
                                                        sinkTopic, consumerRecord.key(), newValue),
                                                consumerRecordString(consumerRecord)));
                        }
                );
    }

    private String consumerRecordString(ConsumerRecord<String, String> consumerRecord) {
        return consumerRecord.topic()
                + "-"
                + consumerRecord.partition()
                + "@"
                + consumerRecord.offset();
    }



    public void stop() {
        this.disposable.dispose();
    }
}
