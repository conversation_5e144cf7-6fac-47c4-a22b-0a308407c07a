package com.illumio.data.test;

import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRebalanceListener;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.TopicPartition;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.apache.kafka.common.serialization.StringSerializer;

import reactor.core.Disposable;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.kafka.receiver.KafkaReceiver;
import reactor.kafka.receiver.ReceiverOptions;
import reactor.kafka.receiver.ReceiverPartition;
import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderOptions;
import reactor.kafka.sender.SenderRecord;
import org.apache.kafka.clients.producer.ProducerRecord;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Kafka simulation utility for ip-classification-mmdb service to test:
 * 1. Consumer timeout scenarios
 * 2. Consumer group rebalancing
 * 3. Partition assignment logging
 */
@Slf4j
public class IpClassificationKafkaSimulator {
    
    private final AtomicInteger consumerInstanceCounter = new AtomicInteger(0);
    private volatile Disposable currentSubscription;
    private final String inputTopic;
    private final String outputTopic;
    private final String consumerGroupId;
    
    public IpClassificationKafkaSimulator() {
        this.inputTopic = "decorated-flow-v1"; // Default input topic for ip-classification
        this.outputTopic = "ip-classified-flow-v1"; // Default output topic
        this.consumerGroupId = "ip-classification-mmdb-simulator";
    }
    
    public IpClassificationKafkaSimulator(String inputTopic, String outputTopic, String consumerGroupId) {
        this.inputTopic = inputTopic;
        this.outputTopic = outputTopic;
        this.consumerGroupId = consumerGroupId;
    }
    
    /**
     * Simulates consumer timeout by creating a consumer with aggressive timeout settings
     * and introducing processing delays that exceed those timeouts
     */
    public void simulateConsumerTimeout() {
        log.info("=== Starting Consumer Timeout Simulation ===");
        log.info("Input Topic: {}, Output Topic: {}, Consumer Group: {}", inputTopic, outputTopic, consumerGroupId);
        
        KafkaReceiver<String, String> receiver = createTimeoutProneReceiver();
        KafkaSender<String, String> sender = createSender();
        
        currentSubscription = receiver
                .receiveAutoAck()
                .publishOn(Schedulers.boundedElastic())
                .doOnSubscribe(subscription -> {
                    int instanceId = consumerInstanceCounter.incrementAndGet();
                    log.info("🚀 Consumer instance {} started", instanceId);
                })
                .doOnNext(records -> {
                    log.info("📦 Received batch of {} records", records.count());
                    // Simulate processing delay that might cause timeout
                    try {
                        Thread.sleep(2000); // 2 second delay
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                })
                .flatMap(records -> records)
                .doOnNext(record -> {
                    log.info("🔄 Processing record from partition {} offset {} - Key: {}", 
                        record.partition(), record.offset(), record.key());
                    // Simulate slow processing that exceeds max.poll.interval.ms
                    simulateSlowProcessing();
                })
                .map(record -> {
                    // Simulate IP classification processing
                    String enrichedValue = enrichWithIpClassification(record.value());
                    return SenderRecord.create(
                        new ProducerRecord<>(outputTopic, record.key(), enrichedValue),
                        record.key()
                    );
                })
                .as(sender::send)
                .doOnNext(result -> {
                    if (result.exception() != null) {
                        log.error("❌ Send failed: {}", result.exception().getMessage());
                    } else {
                        log.info("✅ Successfully sent record: {}", result.correlationMetadata());
                    }
                })
                .retryWhen(
                    Retry.backoff(5, Duration.ofSeconds(3))
                        .doBeforeRetry(retrySignal -> {
                            log.warn("🔄 Consumer error, retrying... Attempt: {}, Error: {}", 
                                retrySignal.totalRetries() + 1, 
                                retrySignal.failure().getMessage());
                        })
                )
                .doOnError(error -> {
                    log.error("💥 Consumer error: {}", error.getMessage(), error);
                })
                .doOnComplete(() -> {
                    log.warn("⚠️ Consumer completed unexpectedly");
                })
                .doOnCancel(() -> {
                    log.warn("🛑 Consumer cancelled");
                })
                .subscribe(
                    result -> log.debug("Processed result: {}", result.correlationMetadata()),
                    error -> log.error("💥 Final consumer error: {}", error.getMessage(), error),
                    () -> log.info("✅ Consumer stream completed")
                );
    }
    
    /**
     * Simulates consumer group rebalancing by creating multiple consumers
     * and then stopping/starting them to trigger rebalances
     */
    public void simulateConsumerGroupRebalance() {
        log.info("=== Starting Consumer Group Rebalance Simulation ===");
        
        List<Disposable> consumers = new ArrayList<>();
        
        // Start multiple consumers to form a group
        for (int i = 0; i < 3; i++) {
            final int consumerId = i;
            KafkaReceiver<String, String> receiver = createRebalanceAwareReceiver(consumerId);
            
            Disposable consumer = receiver
                    .receiveAutoAck()
                    .publishOn(Schedulers.boundedElastic())
                    .doOnSubscribe(subscription -> {
                        log.info("🚀 Consumer {} joined the group", consumerId);
                    })
                    .flatMap(records -> records)
                    .doOnNext(record -> {
                        log.info("🔄 Consumer {} processing record from partition {} offset {}", 
                            consumerId, record.partition(), record.offset());
                        // Light processing to avoid timeouts during rebalance demo
                        try {
                            Thread.sleep(100);
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                        }
                    })
                    .subscribe(
                        record -> log.debug("Consumer {} processed record: {}", consumerId, record.key()),
                        error -> log.error("Consumer {} error: {}", consumerId, error.getMessage()),
                        () -> log.info("Consumer {} completed", consumerId)
                    );
            
            consumers.add(consumer);
            
            // Stagger consumer startup
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        
        // Simulate rebalancing by stopping and starting consumers
        Mono.delay(Duration.ofSeconds(10))
            .doOnNext(tick -> {
                log.info("🔄 Triggering rebalance by stopping consumer 1");
                consumers.get(1).dispose();
            })
            .then(Mono.delay(Duration.ofSeconds(10)))
            .doOnNext(tick -> {
                log.info("🔄 Triggering another rebalance by stopping consumer 2");
                consumers.get(2).dispose();
            })
            .then(Mono.delay(Duration.ofSeconds(10)))
            .doOnNext(tick -> {
                log.info("🔄 Adding new consumer to trigger rebalance");
                KafkaReceiver<String, String> newReceiver = createRebalanceAwareReceiver(99);
                Disposable newConsumer = newReceiver
                        .receiveAutoAck()
                        .publishOn(Schedulers.boundedElastic())
                        .flatMap(records -> records)
                        .subscribe(
                            record -> log.info("New consumer processed: {}", record.key()),
                            error -> log.error("New consumer error: {}", error.getMessage())
                        );
                consumers.add(newConsumer);
            })
            .subscribe();
        
        // Store reference to clean up later
        this.currentSubscription = () -> consumers.forEach(Disposable::dispose);
    }
    
    /**
     * Creates a receiver with partition assignment logging
     */
    private KafkaReceiver<String, String> createRebalanceAwareReceiver(int consumerId) {
        Map<String, Object> consumerProps = createBaseConsumerProps();
        consumerProps.put(ConsumerConfig.GROUP_ID_CONFIG, consumerGroupId);
        consumerProps.put(ConsumerConfig.CLIENT_ID_CONFIG, "consumer-" + consumerId);
        
        // More reasonable timeouts for rebalance demo
        consumerProps.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, 30000);      // 30 seconds
        consumerProps.put(ConsumerConfig.HEARTBEAT_INTERVAL_MS_CONFIG, 10000);   // 10 seconds
        consumerProps.put(ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG, 60000);    // 60 seconds
        
        ReceiverOptions<String, String> receiverOptions = ReceiverOptions
                .<String, String>create(consumerProps)
                .subscription(Collections.singleton(inputTopic))
                .addAssignListener(partitions -> {
                    log.info("🎯 Consumer {} assigned partitions: {}", consumerId, 
                        partitions.stream().map(ReceiverPartition::topicPartition).toList());
                })
                .addRevokeListener(partitions -> {
                    log.info("🔄 Consumer {} revoked partitions: {}", consumerId,
                        partitions.stream().map(ReceiverPartition::topicPartition).toList());
                });
                
        return KafkaReceiver.create(receiverOptions);
    }
    
    /**
     * Creates a receiver with timeout-prone configuration
     */
    private KafkaReceiver<String, String> createTimeoutProneReceiver() {
        Map<String, Object> consumerProps = createBaseConsumerProps();
        consumerProps.put(ConsumerConfig.GROUP_ID_CONFIG, consumerGroupId + "-timeout");
        
        // Very aggressive timeout settings to trigger timeouts quickly
        consumerProps.put(ConsumerConfig.REQUEST_TIMEOUT_MS_CONFIG, 5000);       // 5 seconds
        consumerProps.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, 10000);      // 10 seconds  
        consumerProps.put(ConsumerConfig.HEARTBEAT_INTERVAL_MS_CONFIG, 3000);    // 3 seconds
        consumerProps.put(ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG, 15000);    // 15 seconds
        consumerProps.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, 5);            // Small batches
        
        ReceiverOptions<String, String> receiverOptions = ReceiverOptions
                .<String, String>create(consumerProps)
                .subscription(Collections.singleton(inputTopic))
                .addAssignListener(partitions -> {
                    log.info("🎯 Timeout-prone consumer assigned partitions: {}", 
                        partitions.stream().map(ReceiverPartition::topicPartition).toList());
                })
                .addRevokeListener(partitions -> {
                    log.info("🔄 Timeout-prone consumer revoked partitions: {}", 
                        partitions.stream().map(ReceiverPartition::topicPartition).toList());
                });
                
        return KafkaReceiver.create(receiverOptions);
    }
    
    /**
     * Creates base consumer properties
     */
    private Map<String, Object> createBaseConsumerProps() {
        Map<String, Object> consumerProps = new HashMap<>();
        consumerProps.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, "localhost:9092");
        consumerProps.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");
        consumerProps.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        consumerProps.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        consumerProps.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);
        return consumerProps;
    }
    
    /**
     * Creates a Kafka sender for output
     */
    private KafkaSender<String, String> createSender() {
        Map<String, Object> producerProps = new HashMap<>();
        producerProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, "localhost:9092");
        producerProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        producerProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        producerProps.put(ProducerConfig.ACKS_CONFIG, "all");
        producerProps.put(ProducerConfig.RETRIES_CONFIG, 3);
        
        SenderOptions<String, String> senderOptions = SenderOptions.create(producerProps);
        return KafkaSender.create(senderOptions);
    }
    
    /**
     * Simulates slow processing that might exceed max.poll.interval.ms
     */
    private void simulateSlowProcessing() {
        try {
            // Simulate processing that takes longer than max.poll.interval.ms (15 seconds)
            Thread.sleep(18000); // 18 seconds - this should cause timeout
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("⚠️ Processing interrupted");
        }
    }
    
    /**
     * Simulates IP classification enrichment
     */
    private String enrichWithIpClassification(String originalValue) {
        // Simple simulation - in real service this would do MMDB lookup
        return originalValue.replace("}", ", \"ip_classification\": \"simulated_classification\"}");
    }
    
    /**
     * Prints current partition assignments for a consumer group
     */
    public void printPartitionAssignments() {
        log.info("=== Current Partition Assignments ===");
        log.info("Consumer Group: {}", consumerGroupId);
        log.info("Input Topic: {}", inputTopic);
        log.info("Output Topic: {}", outputTopic);
        log.info("Note: Use Kafka admin tools to see detailed partition assignments:");
        log.info("kafka-consumer-groups.sh --bootstrap-server localhost:9092 --group {} --describe", consumerGroupId);
    }
    
    /**
     * Stops the current simulation
     */
    public void stopSimulation() {
        if (currentSubscription != null && !currentSubscription.isDisposed()) {
            log.info("🛑 Stopping simulation...");
            currentSubscription.dispose();
        }
    }
    
    /**
     * Demonstrates all simulation scenarios
     */
    public void runAllSimulations() {
        log.info("🎬 Starting comprehensive Kafka simulation for ip-classification-mmdb");
        
        printPartitionAssignments();
        
        // Run timeout simulation for 30 seconds
        log.info("📍 Phase 1: Consumer Timeout Simulation (30 seconds)");
        simulateConsumerTimeout();
        
        Mono.delay(Duration.ofSeconds(30))
            .doOnNext(tick -> {
                stopSimulation();
                log.info("📍 Phase 2: Consumer Group Rebalance Simulation (60 seconds)");
                simulateConsumerGroupRebalance();
            })
            .then(Mono.delay(Duration.ofSeconds(60)))
            .doOnNext(tick -> {
                stopSimulation();
                log.info("✅ All simulations completed");
            })
            .subscribe();
    }
}
