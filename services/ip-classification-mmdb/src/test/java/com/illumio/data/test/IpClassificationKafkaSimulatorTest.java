package com.illumio.data.test;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInfo;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.Disabled;
import reactor.core.publisher.Mono;

import java.time.Duration;

/**
 * Test class for running Kafka simulations for ip-classification-mmdb service.
 *
 * To run these tests, you need:
 * 1. A running Kafka instance on localhost:9092
 * 2. Topics: decorated-flow-v1 (input) and ip-classified-flow-v1 (output)
 *
 * These tests are disabled by default. To enable them:
 * 1. Remove @Disabled annotation from the test method you want to run
 * 2. Ensure Kafka is running on localhost:9092
 * 3. Run the test
 *
 * Example commands:
 * ./gradlew :services:ip-classification-mmdb:test --tests IpClassificationKafkaSimulatorTest.testConsumerTimeout
 * ./gradlew :services:ip-classification-mmdb:test --tests IpClassificationKafkaSimulatorTest.testConsumerGroupRebalance
 * ./gradlew :services:ip-classification-mmdb:test --tests IpClassificationKafkaSimulatorTest.testAllSimulations
 */
@Slf4j
public class IpClassificationKafkaSimulatorTest {
    
    private IpClassificationKafkaSimulator simulator;
    
    @BeforeEach
    void setUp(TestInfo testInfo) {
        log.info("🎬 Starting test: {}", testInfo.getDisplayName());
        simulator = new IpClassificationKafkaSimulator();
        
        // Print setup information
        log.info("📋 Test Setup:");
        log.info("  - Kafka Bootstrap Servers: localhost:9092");
        log.info("  - Input Topic: decorated-flow-v1");
        log.info("  - Output Topic: ip-classified-flow-v1");
        log.info("  - Consumer Group: ip-classification-mmdb-simulator");
        log.info("");
        log.info("📝 Prerequisites:");
        log.info("  1. Kafka running on localhost:9092");
        log.info("  2. Topics created (decorated-flow-v1, ip-classified-flow-v1)");
        log.info("  3. Optional: Producer sending test messages to decorated-flow-v1");
        log.info("");
    }
    
    @AfterEach
    void tearDown(TestInfo testInfo) {
        log.info("🧹 Cleaning up test: {}", testInfo.getDisplayName());
        if (simulator != null) {
            simulator.cleanup(); // Use cleanup instead of stopSimulation to close AdminClient
        }

        // Give some time for cleanup
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        log.info("✅ Test cleanup completed: {}", testInfo.getDisplayName());
    }
    
    /**
     * Test consumer timeout scenarios
     * This test simulates slow processing that exceeds max.poll.interval.ms
     *
     * NOTE: This test requires Kafka running on localhost:9092
     */
    @Test
    void testConsumerTimeout() {
        log.info("🎯 Testing Consumer Timeout Simulation");
        log.info("This test will:");
        log.info("  1. Create a consumer with aggressive timeout settings");
        log.info("  2. Simulate slow processing (18 seconds) that exceeds max.poll.interval.ms (15 seconds)");
        log.info("  3. Show how the consumer handles timeouts and retries");
        log.info("");
        
        simulator.simulateConsumerTimeout();
        
        // Let the simulation run for 45 seconds to observe timeout behavior
        Mono.delay(Duration.ofSeconds(45))
            .doOnNext(tick -> log.info("⏰ Timeout simulation running... Check logs for timeout behavior"))
            .block();
        
        log.info("✅ Consumer timeout simulation completed");
    }
    
    /**
     * Test consumer group rebalancing
     * This test demonstrates partition reassignment when consumers join/leave
     *
     * Remove @Disabled annotation to run this test
     */
    @Test
    @Disabled("Remove this annotation to run the test - requires Kafka running on localhost:9092")
    void testConsumerGroupRebalance() {
        log.info("🎯 Testing Consumer Group Rebalance Simulation");
        log.info("This test will:");
        log.info("  1. Start 3 consumers in the same consumer group");
        log.info("  2. Show initial partition assignments");
        log.info("  3. Stop consumers one by one to trigger rebalances");
        log.info("  4. Add a new consumer to trigger another rebalance");
        log.info("  5. Log partition assignments throughout the process");
        log.info("");
        
        simulator.simulateConsumerGroupRebalance();
        
        // Let the rebalance simulation run for 60 seconds
        Mono.delay(Duration.ofSeconds(60))
            .doOnNext(tick -> log.info("⏰ Rebalance simulation running... Watch for partition assignment changes"))
            .block();
        
        log.info("✅ Consumer group rebalance simulation completed");
    }
    
    /**
     * Test partition assignment logging
     * This test focuses on showing current partition assignments
     */
    @Test
    void testPartitionAssignmentLogging() {
        log.info("🎯 Testing Partition Assignment Logging");
        log.info("This test will:");
        log.info("  1. Print current partition assignment information");
        log.info("  2. Show how to use Kafka admin tools for detailed inspection");
        log.info("");
        
        simulator.printPartitionAssignments();
        
        // Create a simple consumer to show partition assignments
        IpClassificationKafkaSimulator tempSimulator = new IpClassificationKafkaSimulator(
            "decorated-flow-v1", 
            "ip-classified-flow-v1", 
            "partition-assignment-test-group"
        );
        
        tempSimulator.simulateConsumerGroupRebalance();
        
        // Run for 20 seconds to see assignments
        Mono.delay(Duration.ofSeconds(20))
            .doOnNext(tick -> {
                log.info("📊 Current partition assignments logged above");
                log.info("💡 Use this command to see detailed consumer group info:");
                log.info("   kafka-consumer-groups.sh --bootstrap-server localhost:9092 --group partition-assignment-test-group --describe");
            })
            .block();
        
        tempSimulator.stopSimulation();
        log.info("✅ Partition assignment logging test completed");
    }
    
    /**
     * Run all simulations in sequence
     * This is a comprehensive test that runs all scenarios
     */
    @Test
    void testAllSimulations() {
        log.info("🎯 Running All Kafka Simulations");
        log.info("This comprehensive test will run:");
        log.info("  1. Consumer timeout simulation (30 seconds)");
        log.info("  2. Consumer group rebalance simulation (60 seconds)");
        log.info("  3. Partition assignment logging");
        log.info("");
        log.info("⏱️ Total estimated time: ~90 seconds");
        log.info("");
        
        simulator.runAllSimulations();
        
        // Wait for all simulations to complete
        Mono.delay(Duration.ofSeconds(100))
            .doOnNext(tick -> log.info("⏰ All simulations running... Monitor logs for different scenarios"))
            .block();
        
        log.info("🎉 All Kafka simulations completed successfully!");
        log.info("");
        log.info("📊 Summary of what was tested:");
        log.info("  ✅ Consumer timeout handling");
        log.info("  ✅ Consumer group rebalancing");
        log.info("  ✅ Partition assignment logging");
        log.info("  ✅ Error handling and retry logic");
        log.info("  ✅ Message processing simulation");
    }
    
    /**
     * Test with custom topics and consumer group
     * This test shows how to use the simulator with different configurations
     */
    @Test
    void testWithCustomConfiguration() {
        log.info("🎯 Testing with Custom Configuration");
        
        IpClassificationKafkaSimulator customSimulator = new IpClassificationKafkaSimulator(
            "custom-input-topic",
            "custom-output-topic", 
            "custom-consumer-group"
        );
        
        log.info("🔧 Using custom configuration:");
        log.info("  - Input Topic: custom-input-topic");
        log.info("  - Output Topic: custom-output-topic");
        log.info("  - Consumer Group: custom-consumer-group");
        log.info("");
        
        customSimulator.printPartitionAssignments();
        customSimulator.simulateConsumerTimeout();
        
        // Run for 30 seconds
        Mono.delay(Duration.ofSeconds(30))
            .doOnNext(tick -> log.info("⏰ Custom configuration test running..."))
            .block();
        
        customSimulator.stopSimulation();
        log.info("✅ Custom configuration test completed");
    }

    /**
     * Test advanced rebalance scenario with consumer timeout and replacement
     * This test demonstrates:
     * 1. 4 consumers processing 16 partitions
     * 2. One consumer hanging after 30 seconds
     * 3. Automatic rebalance when replacement consumer joins
     * 4. Partition assignment monitoring every 10 seconds
     *
     * NOTE: This test requires Kafka running on localhost:9092 with topic decorated-flow-v1 having 16 partitions
     */
    @Test
    void testAdvancedRebalanceScenario() {
        log.info("🎯 Testing Advanced Rebalance Scenario");
        log.info("This test will:");
        log.info("  1. Create 4 consumers for topic decorated-flow-v1 (16 partitions)");
        log.info("  2. After 30 seconds, make consumer 2 timeout/hang");
        log.info("  3. Create replacement consumer to trigger rebalance");
        log.info("  4. Monitor partition assignments every 10 seconds");
        log.info("  5. Run for 1 minute total");
        log.info("");
        log.info("⚠️  Prerequisites:");
        log.info("   - Kafka running on localhost:9092");
        log.info("   - Topic 'decorated-flow-v1' with 16 partitions");
        log.info("   - Test messages being produced to the topic");
        log.info("");

        simulator.simulateAdvancedRebalanceScenario();

        // Let the advanced simulation run for 70 seconds (60s simulation + 10s buffer)
        Mono.delay(Duration.ofSeconds(70))
            .doOnNext(tick -> log.info("⏰ Advanced rebalance simulation running... Monitor logs for partition assignments"))
            .block();

        log.info("✅ Advanced rebalance scenario simulation completed!");
        log.info("");
        log.info("📊 What you should have observed:");
        log.info("  ✅ 4 consumers initially processing ~4 partitions each");
        log.info("  ✅ Consumer 2 hanging after 30 seconds");
        log.info("  ✅ Replacement consumer 99 joining after 35 seconds");
        log.info("  ✅ Partition rebalancing among active consumers");
        log.info("  ✅ Partition assignment logs every 10 seconds");
    }
}
